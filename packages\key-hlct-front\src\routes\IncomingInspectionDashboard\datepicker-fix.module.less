/*
 * DatePicker样式修复文件
 * 解决选中状态和今天状态冲突的问题
 */

// 基础弹窗样式
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 4px !important;
  padding: 1rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  min-width: 360px !important;
  width: 360px !important;
  min-height: 420px !important;
  z-index: 9999 !important;

  // 日历头部
  .c7n-pro-calendar-header {
    color: #fff !important;
    border-bottom: 1px solid #1a3a8b !important;
    padding: 12px 16px !important;
    min-height: 48px !important;

    button {
      color: #b9d4ff !important;
      background: rgba(26, 58, 139, 0.3) !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      min-width: 36px !important;
      height: 36px !important;

      &:hover {
        color: #fff !important;
        background-color: #1a3a8b !important;
        border-color: #4a90e2 !important;
      }
    }
  }

  // 日历主体
  .c7n-pro-calendar-body {
    padding: 8px !important;
    min-height: 240px !important;

    table {
      border-spacing: 2px !important;

      thead th {
        color: #b9d4ff !important;
        padding: 12px 8px !important;
        font-weight: bold !important;
        font-size: 14px !important;
      }

      tbody td {
        padding: 2px !important;
        height: 34px !important;

        .c7n-pro-calendar-date {
          color: #ffffff !important;
          background: rgba(26, 58, 139, 0.6) !important;
          border: 1px solid rgba(185, 212, 255, 0.5) !important;
          border-radius: 4px !important;
          width: 30px !important;
          height: 30px !important;
          line-height: 30px !important;
          font-size: 12px !important;
          margin: 1px !important;

          &:hover {
            background-color: #2a4a9b !important;
            border-color: #4a90e2 !important;
          }
        }

        // 选中状态 - 最高优先级
        &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
          background-color: #4a90e2 !important;
          color: #ffffff !important;
          border-color: #4a90e2 !important;
          font-weight: bold !important;
          box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
        }

        // 今天日期 - 仅当不是选中状态时
        &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
          background: rgba(74, 144, 226, 0.4) !important;
          color: #ffffff !important;
          border-color: #4a90e2 !important;
          font-weight: bold !important;
        }

        // 上月和下月日期
        &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
        &.c7n-pro-calendar-next-month-btn-day .c7n-pro-calendar-date {
          color: #888 !important;
          background: rgba(26, 58, 139, 0.2) !important;
          border-color: rgba(185, 212, 255, 0.2) !important;
        }
      }
    }
  }

  // 日历底部
  .c7n-pro-calendar-footer {
    border-top: 1px solid #1a3a8b !important;
    padding: 8px 0 !important;

    .c7n-pro-calendar-now-btn,
    .c7n-pro-calendar-ok-btn {
      color: #b9d4ff !important;
      border: 1px solid #1a3a8b !important;
      border-radius: 2px !important;
      padding: 4px 12px !important;
      margin-left: 8px !important;

      &:hover {
        color: #fff !important;
        background-color: #1a3a8b !important;
        border-color: #4a90e2 !important;
      }
    }
  }
} 