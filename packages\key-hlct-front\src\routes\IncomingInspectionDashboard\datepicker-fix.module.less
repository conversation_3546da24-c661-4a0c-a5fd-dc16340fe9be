/*
 * DatePicker样式修复文件
 * 实现简洁的日历弹窗样式
 */

// 基础弹窗样式 - 完整日历效果
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 8px !important;
  padding: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6) !important;
  min-width: 380px !important;
  width: 380px !important;
  min-height: 420px !important;
  z-index: 9999 !important;

  // 日历头部 - 完整样式
  .c7n-pro-calendar-header {
    color: #fff !important;
    border-bottom: 1px solid #1a3a8b !important;
    padding: 12px 0 !important;
    min-height: 50px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;

    // 导航按钮样式
    button {
      color: #b9d4ff !important;
      background: transparent !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      min-width: 36px !important;
      height: 36px !important;
      font-size: 14px !important;
      cursor: pointer !important;

      &:hover {
        color: #fff !important;
        background-color: rgba(26, 58, 139, 0.5) !important;
        border-color: #4a90e2 !important;
      }
    }

    // 年月显示
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select {
      color: #fff !important;
      font-size: 18px !important;
      font-weight: bold !important;
    }
  }

  // 日历主体 - 完整日期网格
  .c7n-pro-calendar-body {
    padding: 12px !important;
    min-height: 280px !important;

    table {
      border-spacing: 4px !important;
      width: 100% !important;

      // 星期标题行
      thead th {
        color: #b9d4ff !important;
        padding: 12px 8px !important;
        font-weight: bold !important;
        font-size: 14px !important;
        text-align: center !important;
      }

      // 日期行
      tbody td {
        padding: 3px !important;
        height: 40px !important;
        text-align: center !important;

        .c7n-pro-calendar-date {
          color: #ffffff !important;
          background: rgba(26, 58, 139, 0.6) !important;
          border: 1px solid rgba(185, 212, 255, 0.5) !important;
          border-radius: 6px !important;
          width: 36px !important;
          height: 36px !important;
          line-height: 36px !important;
          font-size: 14px !important;
          margin: 0 auto !important;
          cursor: pointer !important;
          transition: all 0.2s ease !important;

          &:hover {
            background-color: #2a4a9b !important;
            border-color: #4a90e2 !important;
            transform: scale(1.05) !important;
          }
        }

        // 选中状态 - 最高优先级
        &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
          background-color: #4a90e2 !important;
          color: #ffffff !important;
          border-color: #4a90e2 !important;
          font-weight: bold !important;
          box-shadow: 0 0 8px rgba(74, 144, 226, 0.6) !important;
        }

        // 今天日期 - 仅当不是选中状态时
        &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
          background: rgba(74, 144, 226, 0.4) !important;
          color: #ffffff !important;
          border-color: #4a90e2 !important;
          font-weight: bold !important;
          box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
        }

        // 上月和下月日期
        &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
        &.c7n-pro-calendar-next-month-btn-day .c7n-pro-calendar-date {
          color: #888 !important;
          background: rgba(26, 58, 139, 0.2) !important;
          border-color: rgba(185, 212, 255, 0.2) !important;
        }
      }
    }
  }

  // 日历底部
  .c7n-pro-calendar-footer {
    border-top: 1px solid #1a3a8b !important;
    padding: 12px 0 !important;
    text-align: right !important;

    .c7n-pro-calendar-now-btn,
    .c7n-pro-calendar-ok-btn {
      color: #b9d4ff !important;
      background: transparent !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 16px !important;
      margin-left: 8px !important;
      font-size: 12px !important;
      cursor: pointer !important;

      &:hover {
        color: #fff !important;
        background-color: rgba(26, 58, 139, 0.5) !important;
        border-color: #4a90e2 !important;
      }
    }
  }
}