/*
 * DatePicker样式修复文件
 * 实现简洁的日历弹窗样式
 */

// 基础弹窗样式 - 完整日历效果，使用更高优先级选择器
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup),
:global(.c7n-pro-popup.custom-datepicker-popup),
:global(.c7n-pro-calendar-picker-popup[class*="custom-datepicker-popup"]) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 8px !important;
  padding: 16px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6) !important;
  min-width: 380px !important;
  width: 380px !important;
  min-height: 420px !important;
  z-index: 9999 !important;

  // 确保所有子元素都显示
  * {
    display: block !important;
  }

  // 特别确保表格元素显示
  table, thead, tbody, tr, td, th {
    display: table !important;
  }

  thead {
    display: table-header-group !important;
  }

  tbody {
    display: table-row-group !important;
  }

  tr {
    display: table-row !important;
  }

  td, th {
    display: table-cell !important;
  }

  // 日历头部 - 完整样式，强制显示
  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: #fff !important;
    border-bottom: 1px solid #1a3a8b !important;
    padding: 12px 0 !important;
    min-height: 50px !important;
    align-items: center !important;
    justify-content: space-between !important;

    // 导航按钮样式
    button {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      background: transparent !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 12px !important;
      min-width: 36px !important;
      height: 36px !important;
      font-size: 14px !important;
      cursor: pointer !important;

      &:hover {
        color: #fff !important;
        background-color: rgba(26, 58, 139, 0.5) !important;
        border-color: #4a90e2 !important;
      }
    }

    // 年月显示
    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #fff !important;
      font-size: 18px !important;
      font-weight: bold !important;
    }
  }

  // 日历主体 - 完整日期网格，强制显示所有元素
  .c7n-pro-calendar-body {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    padding: 12px !important;
    min-height: 280px !important;

    table {
      display: table !important;
      visibility: visible !important;
      opacity: 1 !important;
      border-spacing: 4px !important;
      width: 100% !important;

      // 星期标题行
      thead {
        display: table-header-group !important;
        visibility: visible !important;
        opacity: 1 !important;

        th {
          display: table-cell !important;
          visibility: visible !important;
          opacity: 1 !important;
          color: #b9d4ff !important;
          padding: 12px 8px !important;
          font-weight: bold !important;
          font-size: 14px !important;
          text-align: center !important;
        }
      }

      // 日期行
      tbody {
        display: table-row-group !important;
        visibility: visible !important;
        opacity: 1 !important;

        tr {
          display: table-row !important;
          visibility: visible !important;
          opacity: 1 !important;
        }

        td {
          display: table-cell !important;
          visibility: visible !important;
          opacity: 1 !important;
          padding: 3px !important;
          height: 40px !important;
          text-align: center !important;

          .c7n-pro-calendar-date {
            display: inline-block !important;
            visibility: visible !important;
            opacity: 1 !important;
            color: #ffffff !important;
            background: rgba(26, 58, 139, 0.6) !important;
            border: 1px solid rgba(185, 212, 255, 0.5) !important;
            border-radius: 6px !important;
            width: 36px !important;
            height: 36px !important;
            line-height: 36px !important;
            font-size: 14px !important;
            margin: 0 auto !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;

            &:hover {
              background-color: #2a4a9b !important;
              border-color: #4a90e2 !important;
              transform: scale(1.05) !important;
            }
          }

          // 选中状态 - 最高优先级
          &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
            background-color: #4a90e2 !important;
            color: #ffffff !important;
            border-color: #4a90e2 !important;
            font-weight: bold !important;
            box-shadow: 0 0 8px rgba(74, 144, 226, 0.6) !important;
          }

          // 今天日期 - 仅当不是选中状态时
          &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
            background: rgba(74, 144, 226, 0.4) !important;
            color: #ffffff !important;
            border-color: #4a90e2 !important;
            font-weight: bold !important;
            box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
          }

          // 上月和下月日期
          &.c7n-pro-calendar-last-month-cell .c7n-pro-calendar-date,
          &.c7n-pro-calendar-next-month-btn-day .c7n-pro-calendar-date {
            color: #888 !important;
            background: rgba(26, 58, 139, 0.2) !important;
            border-color: rgba(185, 212, 255, 0.2) !important;
          }
        }
      }
    }
  }

  // 日历底部
  .c7n-pro-calendar-footer {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    border-top: 1px solid #1a3a8b !important;
    padding: 12px 0 !important;
    text-align: right !important;

    .c7n-pro-calendar-now-btn,
    .c7n-pro-calendar-ok-btn {
      display: inline-block !important;
      visibility: visible !important;
      opacity: 1 !important;
      color: #b9d4ff !important;
      background: transparent !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 4px !important;
      padding: 8px 16px !important;
      margin-left: 8px !important;
      font-size: 12px !important;
      cursor: pointer !important;

      &:hover {
        color: #fff !important;
        background-color: rgba(26, 58, 139, 0.5) !important;
        border-color: #4a90e2 !important;
      }
    }
  }
}

// 额外的全局样式确保日历正常显示
:global {
  .c7n-pro-calendar-picker-popup.custom-datepicker-popup {
    .c7n-pro-calendar-header,
    .c7n-pro-calendar-body,
    .c7n-pro-calendar-footer {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }

    .c7n-pro-calendar-body table {
      display: table !important;

      thead, tbody {
        display: table-header-group !important;
      }

      tbody {
        display: table-row-group !important;
      }

      tr {
        display: table-row !important;
      }

      td, th {
        display: table-cell !important;
      }
    }
  }
}