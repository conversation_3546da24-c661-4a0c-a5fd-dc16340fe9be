/*
 * DatePicker样式修复文件
 * 修复选中状态和今天状态冲突的问题
 */

// 只修复必要的样式冲突，不覆盖主样式文件中的完整定义
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  // 确保弹窗正确显示
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;

  // 确保日历头部显示
  .c7n-pro-calendar-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  // 确保日历主体显示
  .c7n-pro-calendar-body {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;

    table {
      display: table !important;
      visibility: visible !important;
      opacity: 1 !important;

      thead {
        display: table-header-group !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      tbody {
        display: table-row-group !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      tr {
        display: table-row !important;
        visibility: visible !important;
        opacity: 1 !important;
      }

      td, th {
        display: table-cell !important;
        visibility: visible !important;
        opacity: 1 !important;
      }
    }
  }

  // 确保日历底部显示
  .c7n-pro-calendar-footer {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  // 修复选中状态和今天状态的冲突
  .c7n-pro-calendar-body tbody td {
    // 选中状态 - 最高优先级
    &.c7n-pro-calendar-selected .c7n-pro-calendar-date {
      background-color: #4a90e2 !important;
      color: #ffffff !important;
      border-color: #4a90e2 !important;
      font-weight: bold !important;
      box-shadow: 0 0 4px rgba(74, 144, 226, 0.4) !important;
    }

    // 今天日期 - 仅当不是选中状态时
    &.c7n-pro-calendar-today:not(.c7n-pro-calendar-selected) .c7n-pro-calendar-date {
      background: rgba(74, 144, 226, 0.4) !important;
      color: #ffffff !important;
      border-color: #4a90e2 !important;
      font-weight: bold !important;
    }
  }
}