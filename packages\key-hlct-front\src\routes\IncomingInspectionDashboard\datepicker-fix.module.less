/*
 * DatePicker样式修复文件
 * 实现简洁的日历弹窗样式
 */

// 基础弹窗样式
:global(.c7n-pro-calendar-picker-popup.custom-datepicker-popup) {
  background-color: #0c1a3e !important;
  border: 1px solid #1a3a8b !important;
  border-radius: 4px !important;
  padding: 0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5) !important;
  min-width: 300px !important;
  width: 300px !important;
  height: 100px !important;
  min-height: 100px !important;
  max-height: 100px !important;
  z-index: 9999 !important;
  overflow: hidden !important;

  // 日历头部
  .c7n-pro-calendar-header {
    color: #fff !important;
    border-bottom: 1px solid #1a3a8b !important;
    padding: 6px 12px !important;
    height: 36px !important;
    min-height: 36px !important;
    max-height: 36px !important;
    background: rgba(26, 58, 139, 0.3) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;

    button {
      color: #b9d4ff !important;
      background: rgba(26, 58, 139, 0.3) !important;
      border: 1px solid rgba(185, 212, 255, 0.3) !important;
      border-radius: 3px !important;
      padding: 2px 6px !important;
      min-width: 24px !important;
      height: 24px !important;
      font-size: 11px !important;
      line-height: 1 !important;

      &:hover {
        color: #fff !important;
        background-color: #1a3a8b !important;
        border-color: #4a90e2 !important;
      }
    }

    .c7n-pro-calendar-year-select,
    .c7n-pro-calendar-month-select {
      color: #fff !important;
      font-size: 13px !important;
      font-weight: bold !important;
    }
  }

  // 日历主体 - 只显示星期标题行
  .c7n-pro-calendar-body {
    padding: 8px 12px 12px 12px !important;
    height: 52px !important;
    min-height: 52px !important;
    max-height: 52px !important;
    overflow: hidden !important;

    table {
      border-spacing: 0 !important;
      width: 100% !important;
      height: 100% !important;

      // 星期标题行
      thead th {
        color: #b9d4ff !important;
        padding: 6px 2px !important;
        font-weight: bold !important;
        font-size: 11px !important;
        text-align: center !important;
        border-bottom: 1px solid rgba(185, 212, 255, 0.2) !important;
        height: 28px !important;
        vertical-align: middle !important;
      }

      // 隐藏日期行
      tbody {
        display: none !important;
      }
    }
  }

  // 隐藏日历底部
  .c7n-pro-calendar-footer {
    display: none !important;
  }

  // 隐藏时间选择相关元素
  .c7n-pro-calendar-time-picker,
  .c7n-pro-calendar-time-picker-panel {
    display: none !important;
  }

  // 隐藏今天按钮等
  .c7n-pro-calendar-today-btn,
  .c7n-pro-calendar-clear-btn,
  .c7n-pro-calendar-ok-btn {
    display: none !important;
  }
}